# HUD Backend Server

Ein Node.js/Express Backend für das HUD System mit WebSocket-Unterstützung für Echtzeit-Updates.

## Features

- **WebSocket-Kommunikation** für Echtzeit-Updates
- **REST API** für HUD-Konfiguration und Datenabfrage
- **SQLite Datenbank** für persistente Speicherung
- **Player Management** - Spielerdaten, Stats, Geld, Job, Location
- **Notification System** - Info, Success, Error, Announcements
- **Chat System** - Global, Local, Team, Admin Chat
- **HUD Settings** - Draggable Positionen, Sichtbarkeit, Skalierung

## Installation

1. **Dependencies installieren:**
```bash
npm install
```

2. **Environment Variables konfigurieren:**
Kopiere `.env.example` zu `.env` und passe die Werte an.

3. **Server starten:**
```bash
# Development
npm run dev

# Production
npm start
```

## API Endpoints

### Player Management
- `GET /api/player` - Alle Spieler abrufen
- `GET /api/player/:playerId` - Spieler nach ID abrufen
- `POST /api/player` - Neuen Spieler erstellen
- `PATCH /api/player/:playerId/stats` - Spieler Stats aktualisieren
- `PATCH /api/player/:playerId/money` - Spieler Geld aktualisieren
- `PATCH /api/player/:playerId/location` - Spieler Location aktualisieren
- `PATCH /api/player/:playerId/job` - Spieler Job aktualisieren

### HUD Management
- `GET /api/hud/notifications/:playerId` - Notifications für Spieler
- `POST /api/hud/notifications` - Notification senden
- `POST /api/hud/announcement` - System Announcement
- `POST /api/hud/business-notification` - Business Notification
- `POST /api/hud/banking-notification` - Banking Notification
- `POST /api/hud/vehicle-notification` - Vehicle Notification
- `GET /api/hud/time` - Aktuelle Serverzeit
- `GET /api/hud/status` - Server Status

### Chat System
- `GET /api/chat/messages` - Chat Nachrichten abrufen
- `GET /api/chat/messages/player/:playerId` - Chat für Spieler
- `POST /api/chat/messages` - Chat Nachricht senden
- `DELETE /api/chat/messages/cleanup` - Alte Nachrichten löschen

### Player Settings
- `GET /api/player/:playerId/settings` - HUD Settings abrufen
- `PATCH /api/player/:playerId/settings` - HUD Settings aktualisieren
- `POST /api/player/:playerId/settings/reset` - Settings zurücksetzen

## WebSocket Events

### Client → Server
- `player:connect` - Spieler verbinden
- `player:updateStats` - Stats aktualisieren
- `player:updateMoney` - Geld aktualisieren
- `player:updateLocation` - Location aktualisieren
- `player:updateJob` - Job aktualisieren
- `player:updateVoice` - Voice/Radio aktualisieren
- `notification:send` - Notification senden
- `chat:send` - Chat Nachricht senden
- `hud:updateSettings` - HUD Settings aktualisieren
- `hud:updatePosition` - Element Position aktualisieren

### Server → Client
- `player:data` - Spielerdaten
- `player:statsUpdated` - Stats aktualisiert
- `player:moneyUpdated` - Geld aktualisiert
- `player:locationUpdated` - Location aktualisiert
- `player:jobUpdated` - Job aktualisiert
- `player:voiceUpdated` - Voice aktualisiert
- `notification:new` - Neue Notification
- `chat:message` - Neue Chat Nachricht
- `chat:history` - Chat Verlauf
- `hud:settings` - HUD Settings
- `hud:settingsUpdated` - Settings aktualisiert
- `hud:positionUpdated` - Position aktualisiert
- `time:update` - Zeit Update (jede Sekunde)
- `player:joined` - Spieler beigetreten
- `player:left` - Spieler verlassen

## Datenbank Schema

### Players
- Spieler-ID, Name, Geld, Schwarzgeld
- Job, Job Grade, Stats (Hunger, Durst, Gesundheit)
- Location, Voice Settings

### Notifications
- Typ (info, success, error, announce)
- Titel, Nachricht, Dauer
- Spieler-spezifisch oder system-weit

### Chat Messages
- Spieler-ID, Name, Nachricht
- Chat-Typ (global, local, team, admin)
- Zeitstempel

### HUD Settings
- Sichtbarkeit von Elementen
- Positionen für draggable Elemente
- HUD Skalierung

## Beispiel Usage

### Notification senden
```javascript
// REST API
fetch('/api/hud/notifications', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    player_id: 1898,
    type: 'success',
    title: 'Banking',
    message: 'Du hast 1000€ eingezahlt.',
    duration: 4000
  })
});

// WebSocket
socket.emit('notification:send', {
  type: 'info',
  title: 'Information',
  message: 'Fahrzeug abgeschlossen!',
  duration: 4000
});
```

### Player Stats aktualisieren
```javascript
// REST API
fetch('/api/player/1898/stats', {
  method: 'PATCH',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    hunger: 80,
    thirst: 60,
    health: 100
  })
});

// WebSocket
socket.emit('player:updateStats', {
  hunger: 80,
  thirst: 60,
  health: 100
});
```

## Health Check

Server Status: `GET /health`

```json
{
  "status": "OK",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "uptime": 3600
}
```
