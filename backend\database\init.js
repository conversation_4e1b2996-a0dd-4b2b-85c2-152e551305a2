import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const DB_PATH = process.env.DB_PATH || join(__dirname, 'hud.db');

// Ensure database directory exists
async function ensureDbDirectory() {
  const dbDir = dirname(DB_PATH);
  try {
    await fs.access(dbDir);
  } catch {
    await fs.mkdir(dbDir, { recursive: true });
  }
}

// Create database connection
export function createConnection() {
  return new sqlite3.Database(DB_PATH, (err) => {
    if (err) {
      console.error('Error opening database:', err.message);
    } else {
      console.log('Connected to SQLite database');
    }
  });
}

// Initialize database with tables
export async function initDatabase() {
  await ensureDbDirectory();
  
  const db = createConnection();
  
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Players table
      db.run(`
        CREATE TABLE IF NOT EXISTS players (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          player_id INTEGER UNIQUE NOT NULL,
          name TEXT NOT NULL,
          money INTEGER DEFAULT 0,
          black_money INTEGER DEFAULT 0,
          job TEXT DEFAULT 'Arbeitslos',
          job_grade TEXT DEFAULT 'Sozialhilfe',
          hunger INTEGER DEFAULT 100,
          thirst INTEGER DEFAULT 100,
          health INTEGER DEFAULT 100,
          armor INTEGER DEFAULT 0,
          location_street TEXT DEFAULT 'Unbekannt',
          location_area TEXT DEFAULT 'Unbekannt 1000',
          voice_range INTEGER DEFAULT 3,
          radio_channel INTEGER DEFAULT 0,
          is_talking BOOLEAN DEFAULT 0,
          is_radio_active BOOLEAN DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // HUD Settings table
      db.run(`
        CREATE TABLE IF NOT EXISTS hud_settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          player_id INTEGER NOT NULL,
          show_job BOOLEAN DEFAULT 1,
          show_location BOOLEAN DEFAULT 1,
          show_money BOOLEAN DEFAULT 1,
          show_stats BOOLEAN DEFAULT 1,
          hud_scale REAL DEFAULT 1.0,
          position_job_x INTEGER DEFAULT 0,
          position_job_y INTEGER DEFAULT 0,
          position_location_x INTEGER DEFAULT 0,
          position_location_y INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (player_id) REFERENCES players (player_id)
        )
      `);

      // Notifications table
      db.run(`
        CREATE TABLE IF NOT EXISTS notifications (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          player_id INTEGER,
          type TEXT NOT NULL CHECK (type IN ('info', 'success', 'error', 'announce')),
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          duration INTEGER DEFAULT 5000,
          is_read BOOLEAN DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (player_id) REFERENCES players (player_id)
        )
      `);

      // Chat messages table
      db.run(`
        CREATE TABLE IF NOT EXISTS chat_messages (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          player_id INTEGER NOT NULL,
          player_name TEXT NOT NULL,
          message TEXT NOT NULL,
          chat_type TEXT DEFAULT 'global' CHECK (chat_type IN ('global', 'local', 'team', 'admin')),
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (player_id) REFERENCES players (player_id)
        )
      `);

      // Server events table (for announcements, etc.)
      db.run(`
        CREATE TABLE IF NOT EXISTS server_events (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          event_type TEXT NOT NULL,
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          duration INTEGER DEFAULT 10000,
          is_active BOOLEAN DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          expires_at DATETIME
        )
      `);

      // Create indexes for better performance
      db.run(`CREATE INDEX IF NOT EXISTS idx_players_player_id ON players (player_id)`);
      db.run(`CREATE INDEX IF NOT EXISTS idx_notifications_player_id ON notifications (player_id)`);
      db.run(`CREATE INDEX IF NOT EXISTS idx_chat_messages_player_id ON chat_messages (player_id)`);
      db.run(`CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages (created_at)`);
      db.run(`CREATE INDEX IF NOT EXISTS idx_hud_settings_player_id ON hud_settings (player_id)`);
    });

    db.close((err) => {
      if (err) {
        console.error('Error closing database:', err.message);
        reject(err);
      } else {
        console.log('Database initialized successfully');
        resolve();
      }
    });
  });
}
