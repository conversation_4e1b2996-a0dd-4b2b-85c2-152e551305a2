import { createConnection } from '../database/init.js';

export class ChatMessage {
  constructor(data = {}) {
    this.id = data.id || null;
    this.player_id = data.player_id || null;
    this.player_name = data.player_name || '';
    this.message = data.message || '';
    this.chat_type = data.chat_type || 'global'; // global, local, team, admin
    this.created_at = data.created_at || null;
  }

  // Create new chat message
  static async create(messageData) {
    const db = createConnection();
    const chatMessage = new ChatMessage(messageData);
    
    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO chat_messages (player_id, player_name, message, chat_type)
        VALUES (?, ?, ?, ?)
      `;
      
      const params = [
        chatMessage.player_id,
        chatMessage.player_name,
        chatMessage.message,
        chatMessage.chat_type
      ];
      
      db.run(sql, params, function(err) {
        db.close();
        if (err) {
          reject(err);
        } else {
          chatMessage.id = this.lastID;
          resolve(chatMessage);
        }
      });
    });
  }

  // Get recent chat messages
  static async getRecent(chatType = 'global', limit = 50) {
    const db = createConnection();
    
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT * FROM chat_messages 
        WHERE chat_type = ?
        ORDER BY created_at DESC 
        LIMIT ?
      `;
      
      db.all(sql, [chatType, limit], (err, rows) => {
        db.close();
        if (err) {
          reject(err);
        } else {
          const messages = rows.map(row => new ChatMessage(row)).reverse();
          resolve(messages);
        }
      });
    });
  }

  // Get chat messages for specific player
  static async getByPlayerId(playerId, limit = 100) {
    const db = createConnection();
    
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT * FROM chat_messages 
        WHERE player_id = ?
        ORDER BY created_at DESC 
        LIMIT ?
      `;
      
      db.all(sql, [playerId, limit], (err, rows) => {
        db.close();
        if (err) {
          reject(err);
        } else {
          const messages = rows.map(row => new ChatMessage(row));
          resolve(messages);
        }
      });
    });
  }

  // Delete old messages
  static async cleanup(olderThanDays = 30) {
    const db = createConnection();
    
    return new Promise((resolve, reject) => {
      const sql = `
        DELETE FROM chat_messages 
        WHERE created_at < datetime('now', '-' || ? || ' days')
      `;
      
      db.run(sql, [olderThanDays], function(err) {
        db.close();
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  // Validate message content
  static validateMessage(message) {
    if (!message || typeof message !== 'string') {
      return { valid: false, error: 'Message is required' };
    }
    
    if (message.length > 500) {
      return { valid: false, error: 'Message too long (max 500 characters)' };
    }
    
    if (message.trim().length === 0) {
      return { valid: false, error: 'Message cannot be empty' };
    }
    
    return { valid: true };
  }

  // Convert to JSON for API responses
  toJSON() {
    return {
      id: this.id,
      player_id: this.player_id,
      player_name: this.player_name,
      message: this.message,
      chat_type: this.chat_type,
      created_at: this.created_at
    };
  }
}
