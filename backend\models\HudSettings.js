import { createConnection } from '../database/init.js';

export class HudSettings {
  constructor(data = {}) {
    this.id = data.id || null;
    this.player_id = data.player_id || null;
    this.show_job = data.show_job !== undefined ? data.show_job : true;
    this.show_location = data.show_location !== undefined ? data.show_location : true;
    this.show_money = data.show_money !== undefined ? data.show_money : true;
    this.show_stats = data.show_stats !== undefined ? data.show_stats : true;
    this.hud_scale = data.hud_scale || 1.0;
    this.position_job_x = data.position_job_x || 0;
    this.position_job_y = data.position_job_y || 0;
    this.position_location_x = data.position_location_x || 0;
    this.position_location_y = data.position_location_y || 0;
    this.created_at = data.created_at || null;
    this.updated_at = data.updated_at || null;
  }

  // Create default settings for player
  static async create(playerId) {
    const db = createConnection();
    const settings = new HudSettings({ player_id: playerId });
    
    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO hud_settings (
          player_id, show_job, show_location, show_money, show_stats,
          hud_scale, position_job_x, position_job_y, 
          position_location_x, position_location_y
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const params = [
        settings.player_id, settings.show_job, settings.show_location,
        settings.show_money, settings.show_stats, settings.hud_scale,
        settings.position_job_x, settings.position_job_y,
        settings.position_location_x, settings.position_location_y
      ];
      
      db.run(sql, params, function(err) {
        db.close();
        if (err) {
          reject(err);
        } else {
          settings.id = this.lastID;
          resolve(settings);
        }
      });
    });
  }

  // Get settings by player ID
  static async getByPlayerId(playerId) {
    const db = createConnection();
    
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM hud_settings WHERE player_id = ?';
      
      db.get(sql, [playerId], (err, row) => {
        db.close();
        if (err) {
          reject(err);
        } else if (row) {
          resolve(new HudSettings(row));
        } else {
          // Create default settings if none exist
          HudSettings.create(playerId)
            .then(resolve)
            .catch(reject);
        }
      });
    });
  }

  // Update settings
  async update() {
    const db = createConnection();
    
    return new Promise((resolve, reject) => {
      const sql = `
        UPDATE hud_settings SET
          show_job = ?, show_location = ?, show_money = ?, show_stats = ?,
          hud_scale = ?, position_job_x = ?, position_job_y = ?,
          position_location_x = ?, position_location_y = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE player_id = ?
      `;
      
      const params = [
        this.show_job, this.show_location, this.show_money, this.show_stats,
        this.hud_scale, this.position_job_x, this.position_job_y,
        this.position_location_x, this.position_location_y,
        this.player_id
      ];
      
      db.run(sql, params, function(err) {
        db.close();
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  // Update specific setting
  async updateSetting(key, value) {
    if (this.hasOwnProperty(key)) {
      this[key] = value;
      await this.update();
      return true;
    }
    return false;
  }

  // Update position for draggable elements
  async updatePosition(element, x, y) {
    const positionKeys = {
      'job': ['position_job_x', 'position_job_y'],
      'location': ['position_location_x', 'position_location_y']
    };
    
    if (positionKeys[element]) {
      this[positionKeys[element][0]] = x;
      this[positionKeys[element][1]] = y;
      await this.update();
      return { x, y };
    }
    
    return null;
  }

  // Reset to default settings
  async reset() {
    this.show_job = true;
    this.show_location = true;
    this.show_money = true;
    this.show_stats = true;
    this.hud_scale = 1.0;
    this.position_job_x = 0;
    this.position_job_y = 0;
    this.position_location_x = 0;
    this.position_location_y = 0;
    
    await this.update();
    return this;
  }

  // Validate settings data
  static validateSettings(data) {
    const errors = [];
    
    if (data.hud_scale !== undefined) {
      if (typeof data.hud_scale !== 'number' || data.hud_scale < 0.5 || data.hud_scale > 2.0) {
        errors.push('HUD scale must be between 0.5 and 2.0');
      }
    }
    
    const positionFields = [
      'position_job_x', 'position_job_y',
      'position_location_x', 'position_location_y'
    ];
    
    positionFields.forEach(field => {
      if (data[field] !== undefined) {
        if (typeof data[field] !== 'number' || data[field] < -1000 || data[field] > 1000) {
          errors.push(`${field} must be between -1000 and 1000`);
        }
      }
    });
    
    return errors;
  }

  // Convert to JSON for API responses
  toJSON() {
    return {
      id: this.id,
      player_id: this.player_id,
      show_job: this.show_job,
      show_location: this.show_location,
      show_money: this.show_money,
      show_stats: this.show_stats,
      hud_scale: this.hud_scale,
      positions: {
        job: { x: this.position_job_x, y: this.position_job_y },
        location: { x: this.position_location_x, y: this.position_location_y }
      },
      created_at: this.created_at,
      updated_at: this.updated_at
    };
  }
}
