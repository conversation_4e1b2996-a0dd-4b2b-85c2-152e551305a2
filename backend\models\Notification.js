import { createConnection } from '../database/init.js';

export class Notification {
  constructor(data = {}) {
    this.id = data.id || null;
    this.player_id = data.player_id || null;
    this.type = data.type || 'info'; // info, success, error, announce
    this.title = data.title || '';
    this.message = data.message || '';
    this.duration = data.duration || 5000;
    this.is_read = data.is_read || false;
    this.created_at = data.created_at || null;
  }

  // Create new notification
  static async create(notificationData) {
    const db = createConnection();
    const notification = new Notification(notificationData);
    
    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO notifications (player_id, type, title, message, duration)
        VALUES (?, ?, ?, ?, ?)
      `;
      
      const params = [
        notification.player_id,
        notification.type,
        notification.title,
        notification.message,
        notification.duration
      ];
      
      db.run(sql, params, function(err) {
        db.close();
        if (err) {
          reject(err);
        } else {
          notification.id = this.lastID;
          resolve(notification);
        }
      });
    });
  }

  // Get notifications for player
  static async getByPlayerId(playerId, limit = 50) {
    const db = createConnection();
    
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT * FROM notifications 
        WHERE player_id = ? OR player_id IS NULL
        ORDER BY created_at DESC 
        LIMIT ?
      `;
      
      db.all(sql, [playerId, limit], (err, rows) => {
        db.close();
        if (err) {
          reject(err);
        } else {
          const notifications = rows.map(row => new Notification(row));
          resolve(notifications);
        }
      });
    });
  }

  // Mark notification as read
  async markAsRead() {
    const db = createConnection();
    
    return new Promise((resolve, reject) => {
      const sql = 'UPDATE notifications SET is_read = 1 WHERE id = ?';
      
      db.run(sql, [this.id], function(err) {
        db.close();
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  // Delete old notifications
  static async cleanup(olderThanDays = 7) {
    const db = createConnection();
    
    return new Promise((resolve, reject) => {
      const sql = `
        DELETE FROM notifications 
        WHERE created_at < datetime('now', '-' || ? || ' days')
      `;
      
      db.run(sql, [olderThanDays], function(err) {
        db.close();
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  // Create system-wide announcement
  static async createAnnouncement(title, message, duration = 10000) {
    return await Notification.create({
      player_id: null, // null means system-wide
      type: 'announce',
      title,
      message,
      duration
    });
  }

  // Get icon for notification type
  getIcon() {
    const icons = {
      info: 'fa-circle-exclamation',
      success: 'fa-circle-check',
      error: 'fa-circle-xmark',
      announce: 'fa-circle-exclamation'
    };
    return icons[this.type] || icons.info;
  }

  // Convert to JSON for API responses
  toJSON() {
    return {
      id: this.id,
      player_id: this.player_id,
      type: this.type,
      title: this.title,
      message: this.message,
      duration: this.duration,
      is_read: this.is_read,
      icon: this.getIcon(),
      created_at: this.created_at
    };
  }
}
