import { createConnection } from '../database/init.js';

export class Player {
  constructor(data = {}) {
    this.id = data.id || null;
    this.player_id = data.player_id || null;
    this.name = data.name || '';
    this.money = data.money || 0;
    this.black_money = data.black_money || 0;
    this.job = data.job || 'Arbeitslos';
    this.job_grade = data.job_grade || 'Sozialhilfe';
    this.hunger = data.hunger || 100;
    this.thirst = data.thirst || 100;
    this.health = data.health || 100;
    this.armor = data.armor || 0;
    this.location_street = data.location_street || 'Unbekannt';
    this.location_area = data.location_area || 'Unbekannt 1000';
    this.voice_range = data.voice_range || 3;
    this.radio_channel = data.radio_channel || 0;
    this.is_talking = data.is_talking || false;
    this.is_radio_active = data.is_radio_active || false;
    this.created_at = data.created_at || null;
    this.updated_at = data.updated_at || null;
  }

  // Create new player
  static async create(playerData) {
    const db = createConnection();
    const player = new Player(playerData);
    
    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO players (
          player_id, name, money, black_money, job, job_grade,
          hunger, thirst, health, armor, location_street, location_area,
          voice_range, radio_channel, is_talking, is_radio_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const params = [
        player.player_id, player.name, player.money, player.black_money,
        player.job, player.job_grade, player.hunger, player.thirst,
        player.health, player.armor, player.location_street, player.location_area,
        player.voice_range, player.radio_channel, player.is_talking, player.is_radio_active
      ];
      
      db.run(sql, params, function(err) {
        db.close();
        if (err) {
          reject(err);
        } else {
          player.id = this.lastID;
          resolve(player);
        }
      });
    });
  }

  // Find player by player_id
  static async findByPlayerId(playerId) {
    const db = createConnection();
    
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM players WHERE player_id = ?';
      
      db.get(sql, [playerId], (err, row) => {
        db.close();
        if (err) {
          reject(err);
        } else if (row) {
          resolve(new Player(row));
        } else {
          resolve(null);
        }
      });
    });
  }

  // Update player data
  async update() {
    const db = createConnection();
    
    return new Promise((resolve, reject) => {
      const sql = `
        UPDATE players SET
          name = ?, money = ?, black_money = ?, job = ?, job_grade = ?,
          hunger = ?, thirst = ?, health = ?, armor = ?,
          location_street = ?, location_area = ?, voice_range = ?,
          radio_channel = ?, is_talking = ?, is_radio_active = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE player_id = ?
      `;
      
      const params = [
        this.name, this.money, this.black_money, this.job, this.job_grade,
        this.hunger, this.thirst, this.health, this.armor,
        this.location_street, this.location_area, this.voice_range,
        this.radio_channel, this.is_talking, this.is_radio_active,
        this.player_id
      ];
      
      db.run(sql, params, function(err) {
        db.close();
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  // Get all players
  static async getAll() {
    const db = createConnection();
    
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM players ORDER BY created_at DESC';
      
      db.all(sql, [], (err, rows) => {
        db.close();
        if (err) {
          reject(err);
        } else {
          const players = rows.map(row => new Player(row));
          resolve(players);
        }
      });
    });
  }

  // Update specific stats
  async updateStats(stats) {
    const allowedStats = ['hunger', 'thirst', 'health', 'armor'];
    const updates = {};
    
    for (const [key, value] of Object.entries(stats)) {
      if (allowedStats.includes(key)) {
        this[key] = Math.max(0, Math.min(100, value)); // Clamp between 0-100
        updates[key] = this[key];
      }
    }
    
    if (Object.keys(updates).length > 0) {
      await this.update();
      return updates;
    }
    
    return null;
  }

  // Update money
  async updateMoney(money, blackMoney = null) {
    this.money = Math.max(0, money);
    if (blackMoney !== null) {
      this.black_money = Math.max(0, blackMoney);
    }
    await this.update();
    return { money: this.money, black_money: this.black_money };
  }

  // Update location
  async updateLocation(street, area) {
    this.location_street = street || 'Unbekannt';
    this.location_area = area || 'Unbekannt 1000';
    await this.update();
    return { street: this.location_street, area: this.location_area };
  }

  // Update job
  async updateJob(job, grade) {
    this.job = job || 'Arbeitslos';
    this.job_grade = grade || 'Sozialhilfe';
    await this.update();
    return { job: this.job, grade: this.job_grade };
  }

  // Convert to JSON for API responses
  toJSON() {
    return {
      id: this.id,
      player_id: this.player_id,
      name: this.name,
      money: this.money,
      black_money: this.black_money,
      job: this.job,
      job_grade: this.job_grade,
      hunger: this.hunger,
      thirst: this.thirst,
      health: this.health,
      armor: this.armor,
      location: {
        street: this.location_street,
        area: this.location_area
      },
      voice: {
        range: this.voice_range,
        radio_channel: this.radio_channel,
        is_talking: this.is_talking,
        is_radio_active: this.is_radio_active
      },
      created_at: this.created_at,
      updated_at: this.updated_at
    };
  }
}
