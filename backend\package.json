{"name": "hud-backend", "version": "1.0.0", "description": "Backend für HUD System mit WebSocket Support", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.5", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "sqlite3": "^5.1.6", "uuid": "^9.0.1", "joi": "^17.11.0", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "@types/node": "^20.10.0"}, "keywords": ["hud", "websocket", "express", "gaming"], "author": "HUD Backend Developer", "license": "MIT"}