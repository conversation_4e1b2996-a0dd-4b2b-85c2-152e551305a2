import express from 'express';
import { ChatMessage } from '../models/ChatMessage.js';

const router = express.Router();

// Get recent chat messages
router.get('/messages', async (req, res) => {
  try {
    const { chat_type = 'global', limit = 50 } = req.query;
    
    const validChatTypes = ['global', 'local', 'team', 'admin'];
    if (!validChatTypes.includes(chat_type)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid chat type'
      });
    }
    
    const messages = await ChatMessage.getRecent(chat_type, parseInt(limit));
    
    res.json({
      success: true,
      data: messages.map(m => m.toJSON())
    });
  } catch (error) {
    console.error('Error fetching chat messages:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch chat messages'
    });
  }
});

// Get chat messages for specific player
router.get('/messages/player/:playerId', async (req, res) => {
  try {
    const { playerId } = req.params;
    const { limit = 100 } = req.query;
    
    const messages = await ChatMessage.getByPlayerId(
      parseInt(playerId), 
      parseInt(limit)
    );
    
    res.json({
      success: true,
      data: messages.map(m => m.toJSON())
    });
  } catch (error) {
    console.error('Error fetching player chat messages:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch player chat messages'
    });
  }
});

// Send chat message (REST endpoint - mainly for testing)
router.post('/messages', async (req, res) => {
  try {
    const { player_id, player_name, message, chat_type = 'global' } = req.body;
    
    if (!player_id || !player_name || !message) {
      return res.status(400).json({
        success: false,
        error: 'Player ID, player name, and message are required'
      });
    }
    
    // Validate message
    const validation = ChatMessage.validateMessage(message);
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        error: validation.error
      });
    }
    
    const validChatTypes = ['global', 'local', 'team', 'admin'];
    if (!validChatTypes.includes(chat_type)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid chat type'
      });
    }
    
    const chatMessage = await ChatMessage.create({
      player_id,
      player_name,
      message,
      chat_type
    });
    
    res.status(201).json({
      success: true,
      data: chatMessage.toJSON()
    });
  } catch (error) {
    console.error('Error creating chat message:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create chat message'
    });
  }
});

// Cleanup old chat messages
router.delete('/messages/cleanup', async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const deletedCount = await ChatMessage.cleanup(parseInt(days));
    
    res.json({
      success: true,
      data: {
        deleted_count: deletedCount,
        message: `Cleaned up chat messages older than ${days} days`
      }
    });
  } catch (error) {
    console.error('Error cleaning up chat messages:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cleanup chat messages'
    });
  }
});

// Get chat statistics
router.get('/stats', async (req, res) => {
  try {
    // This would require additional database queries
    // For now, return basic stats
    res.json({
      success: true,
      data: {
        total_messages: 0,
        active_players: 0,
        chat_types: ['global', 'local', 'team', 'admin']
      }
    });
  } catch (error) {
    console.error('Error fetching chat stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch chat stats'
    });
  }
});

export default router;
