import express from 'express';
import { Notification } from '../models/Notification.js';
import { sendNotificationToPlayer, sendSystemAnnouncement } from '../socket/handlers.js';

const router = express.Router();

// Get notifications for player
router.get('/notifications/:playerId', async (req, res) => {
  try {
    const { playerId } = req.params;
    const { limit = 50 } = req.query;
    
    const notifications = await Notification.getByPlayerId(
      parseInt(playerId), 
      parseInt(limit)
    );
    
    res.json({
      success: true,
      data: notifications.map(n => n.toJSON())
    });
  } catch (error) {
    console.error('Error fetching notifications:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch notifications'
    });
  }
});

// Send notification to player
router.post('/notifications', async (req, res) => {
  try {
    const { player_id, type, title, message, duration } = req.body;
    
    if (!type || !title || !message) {
      return res.status(400).json({
        success: false,
        error: 'Type, title, and message are required'
      });
    }
    
    const validTypes = ['info', 'success', 'error', 'announce'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid notification type'
      });
    }
    
    const notification = await Notification.create({
      player_id,
      type,
      title,
      message,
      duration: duration || 5000
    });
    
    // Send via WebSocket if player is connected
    if (player_id) {
      sendNotificationToPlayer(player_id, notification.toJSON());
    }
    
    res.status(201).json({
      success: true,
      data: notification.toJSON()
    });
  } catch (error) {
    console.error('Error creating notification:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create notification'
    });
  }
});

// Send system announcement
router.post('/announcement', async (req, res) => {
  try {
    const { title, message, duration } = req.body;
    
    if (!title || !message) {
      return res.status(400).json({
        success: false,
        error: 'Title and message are required'
      });
    }
    
    const announcement = await Notification.createAnnouncement(
      title, 
      message, 
      duration || 10000
    );
    
    // Send to all connected players
    sendSystemAnnouncement(title, message, duration);
    
    res.status(201).json({
      success: true,
      data: announcement.toJSON()
    });
  } catch (error) {
    console.error('Error creating announcement:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create announcement'
    });
  }
});

// Mark notification as read
router.patch('/notifications/:notificationId/read', async (req, res) => {
  try {
    const { notificationId } = req.params;
    
    // This would need to be implemented in the Notification model
    // For now, we'll return success
    res.json({
      success: true,
      message: 'Notification marked as read'
    });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to mark notification as read'
    });
  }
});

// Get current server time
router.get('/time', (req, res) => {
  const now = new Date();
  res.json({
    success: true,
    data: {
      time: now.toLocaleTimeString('de-DE', { hour: '2-digit', minute: '2-digit' }),
      date: now.toLocaleDateString('de-DE'),
      timestamp: now.toISOString(),
      unix: Math.floor(now.getTime() / 1000)
    }
  });
});

// Get server status
router.get('/status', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'online',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: '1.0.0',
      timestamp: new Date().toISOString()
    }
  });
});

// Cleanup old notifications
router.delete('/notifications/cleanup', async (req, res) => {
  try {
    const { days = 7 } = req.query;
    const deletedCount = await Notification.cleanup(parseInt(days));
    
    res.json({
      success: true,
      data: {
        deleted_count: deletedCount,
        message: `Cleaned up notifications older than ${days} days`
      }
    });
  } catch (error) {
    console.error('Error cleaning up notifications:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cleanup notifications'
    });
  }
});

// Send business notification (like casino message)
router.post('/business-notification', async (req, res) => {
  try {
    const { business_name, message, player_id } = req.body;
    
    if (!business_name || !message) {
      return res.status(400).json({
        success: false,
        error: 'Business name and message are required'
      });
    }
    
    const notification = await Notification.create({
      player_id,
      type: 'info',
      title: `[Business Meldung: ${business_name}]`,
      message,
      duration: 7000
    });
    
    // Send via WebSocket
    if (player_id) {
      sendNotificationToPlayer(player_id, notification.toJSON());
    } else {
      sendSystemAnnouncement(notification.title, notification.message, 7000);
    }
    
    res.status(201).json({
      success: true,
      data: notification.toJSON()
    });
  } catch (error) {
    console.error('Error creating business notification:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create business notification'
    });
  }
});

// Send banking notification
router.post('/banking-notification', async (req, res) => {
  try {
    const { amount, action, player_id } = req.body;
    
    if (!amount || !action || !player_id) {
      return res.status(400).json({
        success: false,
        error: 'Amount, action, and player_id are required'
      });
    }
    
    const actionText = action === 'withdraw' ? 'abgehoben' : 'eingezahlt';
    const message = `Du hast ${amount}€ von deinem Konto ${actionText}.`;
    
    const notification = await Notification.create({
      player_id,
      type: 'success',
      title: 'Banking',
      message,
      duration: 4000
    });
    
    sendNotificationToPlayer(player_id, notification.toJSON());
    
    res.status(201).json({
      success: true,
      data: notification.toJSON()
    });
  } catch (error) {
    console.error('Error creating banking notification:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create banking notification'
    });
  }
});

// Send vehicle notification
router.post('/vehicle-notification', async (req, res) => {
  try {
    const { message, type = 'error', player_id } = req.body;
    
    if (!message || !player_id) {
      return res.status(400).json({
        success: false,
        error: 'Message and player_id are required'
      });
    }
    
    const notification = await Notification.create({
      player_id,
      type,
      title: 'Information',
      message,
      duration: 4000
    });
    
    sendNotificationToPlayer(player_id, notification.toJSON());
    
    res.status(201).json({
      success: true,
      data: notification.toJSON()
    });
  } catch (error) {
    console.error('Error creating vehicle notification:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create vehicle notification'
    });
  }
});

export default router;
