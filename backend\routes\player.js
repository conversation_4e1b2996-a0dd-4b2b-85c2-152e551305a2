import express from 'express';
import { Player } from '../models/Player.js';
import { HudSettings } from '../models/HudSettings.js';

const router = express.Router();

// Get all players
router.get('/', async (req, res) => {
  try {
    const players = await Player.getAll();
    res.json({
      success: true,
      data: players.map(p => p.toJSON())
    });
  } catch (error) {
    console.error('Error fetching players:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch players'
    });
  }
});

// Get player by ID
router.get('/:playerId', async (req, res) => {
  try {
    const { playerId } = req.params;
    const player = await Player.findByPlayerId(parseInt(playerId));
    
    if (!player) {
      return res.status(404).json({
        success: false,
        error: 'Player not found'
      });
    }
    
    res.json({
      success: true,
      data: player.toJSON()
    });
  } catch (error) {
    console.error('Error fetching player:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch player'
    });
  }
});

// Create new player
router.post('/', async (req, res) => {
  try {
    const { player_id, name, money = 0, black_money = 0 } = req.body;
    
    if (!player_id || !name) {
      return res.status(400).json({
        success: false,
        error: 'Player ID and name are required'
      });
    }
    
    // Check if player already exists
    const existingPlayer = await Player.findByPlayerId(player_id);
    if (existingPlayer) {
      return res.status(409).json({
        success: false,
        error: 'Player already exists'
      });
    }
    
    const player = await Player.create({
      player_id,
      name,
      money,
      black_money
    });
    
    // Create default HUD settings
    await HudSettings.create(player_id);
    
    res.status(201).json({
      success: true,
      data: player.toJSON()
    });
  } catch (error) {
    console.error('Error creating player:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create player'
    });
  }
});

// Update player stats
router.patch('/:playerId/stats', async (req, res) => {
  try {
    const { playerId } = req.params;
    const player = await Player.findByPlayerId(parseInt(playerId));
    
    if (!player) {
      return res.status(404).json({
        success: false,
        error: 'Player not found'
      });
    }
    
    const updatedStats = await player.updateStats(req.body);
    
    if (!updatedStats) {
      return res.status(400).json({
        success: false,
        error: 'No valid stats provided'
      });
    }
    
    res.json({
      success: true,
      data: updatedStats
    });
  } catch (error) {
    console.error('Error updating player stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update player stats'
    });
  }
});

// Update player money
router.patch('/:playerId/money', async (req, res) => {
  try {
    const { playerId } = req.params;
    const { money, black_money } = req.body;
    
    const player = await Player.findByPlayerId(parseInt(playerId));
    
    if (!player) {
      return res.status(404).json({
        success: false,
        error: 'Player not found'
      });
    }
    
    if (money === undefined && black_money === undefined) {
      return res.status(400).json({
        success: false,
        error: 'Money or black_money is required'
      });
    }
    
    const updatedMoney = await player.updateMoney(money, black_money);
    
    res.json({
      success: true,
      data: updatedMoney
    });
  } catch (error) {
    console.error('Error updating player money:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update player money'
    });
  }
});

// Update player location
router.patch('/:playerId/location', async (req, res) => {
  try {
    const { playerId } = req.params;
    const { street, area } = req.body;
    
    const player = await Player.findByPlayerId(parseInt(playerId));
    
    if (!player) {
      return res.status(404).json({
        success: false,
        error: 'Player not found'
      });
    }
    
    const updatedLocation = await player.updateLocation(street, area);
    
    res.json({
      success: true,
      data: updatedLocation
    });
  } catch (error) {
    console.error('Error updating player location:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update player location'
    });
  }
});

// Update player job
router.patch('/:playerId/job', async (req, res) => {
  try {
    const { playerId } = req.params;
    const { job, grade } = req.body;
    
    const player = await Player.findByPlayerId(parseInt(playerId));
    
    if (!player) {
      return res.status(404).json({
        success: false,
        error: 'Player not found'
      });
    }
    
    const updatedJob = await player.updateJob(job, grade);
    
    res.json({
      success: true,
      data: updatedJob
    });
  } catch (error) {
    console.error('Error updating player job:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update player job'
    });
  }
});

// Get player HUD settings
router.get('/:playerId/settings', async (req, res) => {
  try {
    const { playerId } = req.params;
    const settings = await HudSettings.getByPlayerId(parseInt(playerId));
    
    res.json({
      success: true,
      data: settings.toJSON()
    });
  } catch (error) {
    console.error('Error fetching player settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch player settings'
    });
  }
});

// Update player HUD settings
router.patch('/:playerId/settings', async (req, res) => {
  try {
    const { playerId } = req.params;
    const settings = await HudSettings.getByPlayerId(parseInt(playerId));
    
    // Validate settings
    const errors = HudSettings.validateSettings(req.body);
    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        error: errors.join(', ')
      });
    }
    
    // Update settings
    Object.assign(settings, req.body);
    await settings.update();
    
    res.json({
      success: true,
      data: settings.toJSON()
    });
  } catch (error) {
    console.error('Error updating player settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update player settings'
    });
  }
});

// Reset player HUD settings
router.post('/:playerId/settings/reset', async (req, res) => {
  try {
    const { playerId } = req.params;
    const settings = await HudSettings.getByPlayerId(parseInt(playerId));
    
    await settings.reset();
    
    res.json({
      success: true,
      data: settings.toJSON()
    });
  } catch (error) {
    console.error('Error resetting player settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reset player settings'
    });
  }
});

export default router;
