import { Player } from '../models/Player.js';
import { Notification } from '../models/Notification.js';
import { ChatMessage } from '../models/ChatMessage.js';
import { HudSettings } from '../models/HudSettings.js';

// Store connected players
const connectedPlayers = new Map();

export function setupSocketHandlers(io) {
  io.on('connection', (socket) => {
    console.log(`🔌 Client connected: ${socket.id}`);

    // Player connection
    socket.on('player:connect', async (data) => {
      try {
        const { player_id, name } = data;
        
        // Find or create player
        let player = await Player.findByPlayerId(player_id);
        if (!player) {
          player = await Player.create({ player_id, name });
        }
        
        // Store connection
        connectedPlayers.set(socket.id, {
          player_id,
          name,
          socket_id: socket.id
        });
        
        // Join player room
        socket.join(`player_${player_id}`);
        
        // Send initial player data
        socket.emit('player:data', player.toJSON());
        
        // Send HUD settings
        const settings = await HudSettings.getByPlayerId(player_id);
        socket.emit('hud:settings', settings.toJSON());
        
        // Send recent notifications
        const notifications = await Notification.getByPlayerId(player_id, 10);
        socket.emit('notifications:history', notifications.map(n => n.toJSON()));
        
        // Send recent chat messages
        const chatMessages = await ChatMessage.getRecent('global', 20);
        socket.emit('chat:history', chatMessages.map(m => m.toJSON()));
        
        console.log(`✅ Player ${name} (${player_id}) connected`);
        
        // Notify other players
        socket.broadcast.emit('player:joined', {
          player_id,
          name,
          timestamp: new Date().toISOString()
        });
        
      } catch (error) {
        console.error('Error handling player connection:', error);
        socket.emit('error', { message: 'Failed to connect player' });
      }
    });

    // Player stats update
    socket.on('player:updateStats', async (data) => {
      try {
        const playerInfo = connectedPlayers.get(socket.id);
        if (!playerInfo) return;
        
        const player = await Player.findByPlayerId(playerInfo.player_id);
        if (!player) return;
        
        const updatedStats = await player.updateStats(data);
        if (updatedStats) {
          // Send to player
          socket.emit('player:statsUpdated', updatedStats);
          
          // Broadcast to other players if needed
          socket.broadcast.emit('player:statsChanged', {
            player_id: playerInfo.player_id,
            stats: updatedStats
          });
        }
        
      } catch (error) {
        console.error('Error updating player stats:', error);
        socket.emit('error', { message: 'Failed to update stats' });
      }
    });

    // Player money update
    socket.on('player:updateMoney', async (data) => {
      try {
        const playerInfo = connectedPlayers.get(socket.id);
        if (!playerInfo) return;
        
        const player = await Player.findByPlayerId(playerInfo.player_id);
        if (!player) return;
        
        const { money, black_money } = data;
        const updatedMoney = await player.updateMoney(money, black_money);
        
        socket.emit('player:moneyUpdated', updatedMoney);
        
      } catch (error) {
        console.error('Error updating player money:', error);
        socket.emit('error', { message: 'Failed to update money' });
      }
    });

    // Player location update
    socket.on('player:updateLocation', async (data) => {
      try {
        const playerInfo = connectedPlayers.get(socket.id);
        if (!playerInfo) return;
        
        const player = await Player.findByPlayerId(playerInfo.player_id);
        if (!player) return;
        
        const { street, area } = data;
        const updatedLocation = await player.updateLocation(street, area);
        
        socket.emit('player:locationUpdated', updatedLocation);
        
      } catch (error) {
        console.error('Error updating player location:', error);
        socket.emit('error', { message: 'Failed to update location' });
      }
    });

    // Player job update
    socket.on('player:updateJob', async (data) => {
      try {
        const playerInfo = connectedPlayers.get(socket.id);
        if (!playerInfo) return;
        
        const player = await Player.findByPlayerId(playerInfo.player_id);
        if (!player) return;
        
        const { job, grade } = data;
        const updatedJob = await player.updateJob(job, grade);
        
        socket.emit('player:jobUpdated', updatedJob);
        
      } catch (error) {
        console.error('Error updating player job:', error);
        socket.emit('error', { message: 'Failed to update job' });
      }
    });

    // Voice/Radio updates
    socket.on('player:updateVoice', async (data) => {
      try {
        const playerInfo = connectedPlayers.get(socket.id);
        if (!playerInfo) return;
        
        const player = await Player.findByPlayerId(playerInfo.player_id);
        if (!player) return;
        
        const { is_talking, is_radio_active, voice_range, radio_channel } = data;
        
        if (is_talking !== undefined) player.is_talking = is_talking;
        if (is_radio_active !== undefined) player.is_radio_active = is_radio_active;
        if (voice_range !== undefined) player.voice_range = voice_range;
        if (radio_channel !== undefined) player.radio_channel = radio_channel;
        
        await player.update();
        
        socket.emit('player:voiceUpdated', {
          is_talking: player.is_talking,
          is_radio_active: player.is_radio_active,
          voice_range: player.voice_range,
          radio_channel: player.radio_channel
        });
        
      } catch (error) {
        console.error('Error updating player voice:', error);
        socket.emit('error', { message: 'Failed to update voice' });
      }
    });

    // Send notification
    socket.on('notification:send', async (data) => {
      try {
        const playerInfo = connectedPlayers.get(socket.id);
        if (!playerInfo) return;
        
        const { type, title, message, duration, target_player_id } = data;
        
        const notification = await Notification.create({
          player_id: target_player_id || playerInfo.player_id,
          type,
          title,
          message,
          duration
        });
        
        // Send to target player or current player
        const targetRoom = target_player_id ? `player_${target_player_id}` : socket.id;
        io.to(targetRoom).emit('notification:new', notification.toJSON());
        
      } catch (error) {
        console.error('Error sending notification:', error);
        socket.emit('error', { message: 'Failed to send notification' });
      }
    });

    // Send chat message
    socket.on('chat:send', async (data) => {
      try {
        const playerInfo = connectedPlayers.get(socket.id);
        if (!playerInfo) return;
        
        const { message, chat_type = 'global' } = data;
        
        // Validate message
        const validation = ChatMessage.validateMessage(message);
        if (!validation.valid) {
          socket.emit('error', { message: validation.error });
          return;
        }
        
        const chatMessage = await ChatMessage.create({
          player_id: playerInfo.player_id,
          player_name: playerInfo.name,
          message,
          chat_type
        });
        
        // Broadcast to all players or specific chat type
        if (chat_type === 'global') {
          io.emit('chat:message', chatMessage.toJSON());
        } else {
          // Handle other chat types (local, team, admin) here
          socket.emit('chat:message', chatMessage.toJSON());
        }
        
      } catch (error) {
        console.error('Error sending chat message:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });

    // HUD settings update
    socket.on('hud:updateSettings', async (data) => {
      try {
        const playerInfo = connectedPlayers.get(socket.id);
        if (!playerInfo) return;
        
        const settings = await HudSettings.getByPlayerId(playerInfo.player_id);
        
        // Validate settings
        const errors = HudSettings.validateSettings(data);
        if (errors.length > 0) {
          socket.emit('error', { message: errors.join(', ') });
          return;
        }
        
        // Update settings
        Object.assign(settings, data);
        await settings.update();
        
        socket.emit('hud:settingsUpdated', settings.toJSON());
        
      } catch (error) {
        console.error('Error updating HUD settings:', error);
        socket.emit('error', { message: 'Failed to update settings' });
      }
    });

    // HUD position update (for draggable elements)
    socket.on('hud:updatePosition', async (data) => {
      try {
        const playerInfo = connectedPlayers.get(socket.id);
        if (!playerInfo) return;
        
        const settings = await HudSettings.getByPlayerId(playerInfo.player_id);
        const { element, x, y } = data;
        
        const result = await settings.updatePosition(element, x, y);
        if (result) {
          socket.emit('hud:positionUpdated', { element, ...result });
        }
        
      } catch (error) {
        console.error('Error updating HUD position:', error);
        socket.emit('error', { message: 'Failed to update position' });
      }
    });

    // Player disconnect
    socket.on('disconnect', () => {
      const playerInfo = connectedPlayers.get(socket.id);
      if (playerInfo) {
        console.log(`🔌 Player ${playerInfo.name} (${playerInfo.player_id}) disconnected`);
        
        // Notify other players
        socket.broadcast.emit('player:left', {
          player_id: playerInfo.player_id,
          name: playerInfo.name,
          timestamp: new Date().toISOString()
        });
        
        connectedPlayers.delete(socket.id);
      } else {
        console.log(`🔌 Client disconnected: ${socket.id}`);
      }
    });
  });

  // Periodic updates
  setInterval(() => {
    // Send current time to all connected clients
    const now = new Date();
    io.emit('time:update', {
      time: now.toLocaleTimeString('de-DE', { hour: '2-digit', minute: '2-digit' }),
      date: now.toLocaleDateString('de-DE'),
      timestamp: now.toISOString()
    });
  }, 1000); // Update every second

  console.log('🔌 Socket.IO handlers initialized');
}

// Helper function to send notification to specific player
export function sendNotificationToPlayer(playerId, notification) {
  const io = global.io || require('../server.js').io;
  io.to(`player_${playerId}`).emit('notification:new', notification);
}

// Helper function to send system announcement
export function sendSystemAnnouncement(title, message, duration = 10000) {
  const io = global.io || require('../server.js').io;
  io.emit('notification:new', {
    type: 'announce',
    title,
    message,
    duration,
    timestamp: new Date().toISOString()
  });
}
