{"name": "react-ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build:game": "vite build --watch", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@cseitz/fontawesome-svg-sharp-solid": "^1.0.1", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-transition-group": "^4.4.5", "rollup-plugin-react-scoped-css": "^1.1.0", "sass": "^1.75.0"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "typescript": "^5.2.2", "vite": "^5.2.0"}}